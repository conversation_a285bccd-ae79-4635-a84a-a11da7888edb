<div class="erp-card erp-shadow-end">
    <div class="my-3 row px-3">
        <div class="col-sm-12 col-md-6">
            <div class="d-flex align-items-center">
                <div class="mr-3">
                    {{'SHARED.SHOW' |translate}}
                </div>
                <div class="mx-3">
                    <select class="form-select" [(ngModel)]="pageSize" aria-label="Default select example">
                        <option value="5">5</option>
                        <option value="10">10</option>
                        <option value="50">50</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-sm-12 col-md-3">
            <input type="text" id="search" class="form-control" placeholder="{{'SHARED.TYPEHERETOSEARCH'|translate}}"
                name="search" [(ngModel)]="searchValue">
        </div>
        <div class="col-xs-12 col-sm-12 col-md-3 md:text-center text-align-end"><button (click)="addUserModal()"
                class="btn btn-primary">+ {{'USERS.TABLE.ADDUSER' |translate}}</button></div>
    </div>
    <nz-table #basicTable #sortTable [nzPageSize]="pageSize"
        [nzData]="usersList | tablefilters: {userName: searchValue, email:searchValue, phoneNumber:searchValue, isActive:searchValue}"
        nzTableLayout="fixed">
        <thead>
            <tr>
                <th nzColumnKey="name" [nzSortFn]="sortFnName">{{'USERS.TABLE.USERNAME'|translate}}</th>
                <th nzColumnKey="name" [nzSortFn]="sortFnEmail">{{'USERS.TABLE.EMAIL'|translate}}</th>
                <th nzColumnKey="name" [nzSortFn]="sortFnNumber">{{'USERS.TABLE.PHONENUMBER'|translate}}</th>
                <th nzColumnKey="name" [nzSortFn]="sortFnNActive">{{'USERS.TABLE.STATUS'|translate}}</th>
                <th nzColumnKey="name">{{'USERS.TABLE.ACTIONS'|translate}}</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let data of basicTable.data">
                <td>{{ data.userName }}</td>
                <td>{{ data.email }}</td>
                <td>{{ data.phoneNumber}}</td>
                <td> <button *ngIf=" data.isActive" class="btn btn-danger btn-sm text-white" disabled>
                        {{'SHARED.ACTIVE'|translate}}</button>
                    <button *ngIf="!data.isActive" class="btn btn-primary btn-sm text-white"
                        disabled>{{'SHARED.INACTIVE'|translate}}</button>
                </td>
                <td><span data-bs-toggle="dropdown" aria-expanded="false"><svg xmlns="http://www.w3.org/2000/svg"
                            width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical"
                            viewBox="0 0 16 16">
                            <path
                                d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0" />
                        </svg></span>
                    <ul class="dropdown-menu">
                        <li class="p-2"><button (click)="editUser(data)"
                                class="erp-btn erp-btn-primary">{{"USERS.DROPDOWN.EDITTENANTUSER"|translate}} </button>
                        </li>
                        <li class="p-2"><button (click)="editPassword(data.email)"
                                class="erp-btn erp-btn-babyblue">{{"USERS.DROPDOWN.RESETPASSWORD"|translate}}</button>
                        </li>
                        <li class="p-2"><button (click)="showModal(data.userId)"
                                class="erp-btn erp-btn-gray">{{"USERS.DROPDOWN.EDITUSERPERMISSIONS"|translate}}</button>
                        </li>
                    </ul>
                </td>
            </tr>
        </tbody>
    </nz-table>
  <div #pdfTable id="pdfTable" class="pdf-container">
    <!-- PDF Header -->
    <div style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid lightblue; padding-bottom: 10px;">
      <h2>{{ 'PDF.USERSREPORTS' | translate }}</h2>
      <div style="display: flex; justify-content: space-between; margin-top: 10px;">
        <div>
          {{ 'PDF.DATE' | translate }}: {{ currentDate | date:'yyyy-MM-dd' }}
        </div>
        <div>
          {{ 'PDF.REF' | translate }}: {{ currentRef }}
        </div>
      </div>
    </div>

    <!-- PDF Table -->
    <table class="table table-bordered pdf-table">
      <thead>
      <tr class="text-blue-500">
        <th>{{'USERS.TABLE.USERNAME'|translate}}</th>
        <th>{{'USERS.TABLE.EMAIL'|translate}}</th>
        <th>{{'USERS.TABLE.PHONENUMBER'|translate}}</th>
        <th>{{'USERS.TABLE.STATUS'|translate}}</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let user of flattenedUsers">
        <td>{{ user.userName }}</td>
        <td>{{ user.email }}</td>
        <td>{{ user.phoneNumber}}</td>
        <td><button *ngIf=" user.isActive" class="btn btn-danger btn-sm text-white" disabled>
          {{'SHARED.ACTIVE'|translate}}</button>
          <button *ngIf="!user.isActive" class="btn btn-primary btn-sm text-white"
                  disabled>{{'SHARED.INACTIVE'|translate}}</button></td>
      </tr>
      </tbody>
    </table>

  </div>

  <div class="row mt-3 text-center">
    <app-export-pdf-button
      [tableElement]="pdfTable"
    >
    </app-export-pdf-button>
  </div>
</div>
