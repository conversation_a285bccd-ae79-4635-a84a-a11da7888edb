<div class="erp-card erp-shadow-end">
  <app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>
  <h3 class="text-center my-3 text-primary">{{ "REPORTS.ALLFINALREPORTS" | translate }}</h3>

  <!-- Form Section -->
  <div class="row my-3 px-3">
    <form [formGroup]="form" class="w-100">
      <!-- Accounts Selection -->
      <div class="row mb-3">
        <div class="col-md-4">
          <label class="form-label">{{ "REPORTS.ACCOUNTS"|translate }}</label>
          <p-multiSelect
            [options]="accountsList"
            formControlName="accountIds"
            optionLabel="label"
            styleClass="w-full padding-0.5rem h-3rem flex align-items-center justify-content-center custom-multiselect"
          >
          </p-multiSelect>
        </div>
        <div class="col-md-4">
          <label class="form-label">{{ "REPORTS.ACTIVITY"|translate }}</label>
          <p-multiSelect
            [options]="activityList"
            formControlName="activityIds"
            optionLabel="label"
            styleClass="w-full padding-0.5rem h-3rem flex align-items-center justify-content-center custom-multiselect"
          >
          </p-multiSelect>
        </div>
        <div class="col-md-4">
          <label class="form-label">{{ "REPORTS.COSTCENTER"|translate }}</label>
          <p-multiSelect
            [options]="costList"
            formControlName="costCenterIds"
            optionLabel="label"
            styleClass="w-full padding-0.5rem h-3rem flex align-items-center justify-content-center custom-multiselect"
          >
          </p-multiSelect>
        </div>
      </div>

      <div class="row mb-3">
        <div class="col-md-6">
          <label class="form-label">{{ "REPORTS.PROJECT"|translate }}</label>
          <p-multiSelect
            [options]="projectList"
            formControlName="projectIds"
            optionLabel="label"
            styleClass="w-full padding-0.5rem h-3rem flex align-items-center justify-content-center custom-multiselect"
          >
          </p-multiSelect>
        </div>

        <div class="col-md-6">
          <label class="form-label">{{ "REPORTS.BRANCHES"|translate }}</label>
          <p-multiSelect
            [options]="branchesList"
            formControlName="brancheIds"
            optionLabel="label"
            styleClass="w-full padding-0.5rem h-3rem flex align-items-center justify-content-center custom-multiselect"
          >
          </p-multiSelect>
        </div>
      </div>

      <!-- Date Pickers -->
      <div class="row mb-3">
        <div class="col-md-6">
          <label class="form-label">{{ "SYSTEMSETTINGS.STARTDATE" | translate }}</label>
          <nz-date-picker formControlName="startDate" class="datepicker"></nz-date-picker>
        </div>
        <div class="col-md-6">
          <label class="form-label">{{ "SYSTEMSETTINGS.ENDDATE" | translate }}</label>
          <nz-date-picker formControlName="endDate" class="datepicker"></nz-date-picker>
          <p *ngIf="form.hasError('dateRange')" class="text-danger" style="font-size: 12px;">
            {{ "VALIDATORS.DATERANGE" | translate }}
          </p>
        </div>
      </div>

      <!-- Submit Button -->
      <div class="col-md-4 mx-auto my-4">
        <button type="button" class="btn btn-primary w-100" (click)="onSubmit()">
          {{ "REPORTS.SUBMIT" | translate }}
        </button>
      </div>
    </form>

    <!-- Table Section -->
    <nz-table #basicTable [nzData]="reportList" [nzShowPagination]="false" nzTableLayout="fixed">
      <thead>
      <tr>

        <th>{{ 'REPORTS.DOCUMENTNUMBER' | translate }}</th>
        <th>{{ 'ACCOUNT.ADDACCOUNTFORM.ACCOUNTNAME' | translate }}</th>
        <th>{{ 'REPORTS.TOTAL_DEBIT' | translate }}</th>
        <th>{{ 'REPORTS.TOTAL_CREDIT' | translate }}</th>
        <th>{{ 'REPORTS.DOCUMENTNAME' | translate }}</th>

        <th>{{ 'REPORTS.MOVEMENTDATE' | translate }}</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let i of reportList; let idx = index" [hidden]="idx % 2 !== 0">
        <td>
          {{ reportList[idx].documentNumber }}
        </td>
        <td>
          {{ reportList[idx].account.number }} -
          {{ lang == "en" ? reportList[idx].account.nameEn : reportList[idx].account.nameAr }}
        </td>
        <td>{{ reportList[idx].totalDebit | number: '1.2-2' }}</td>
        <td *ngIf="reportList[idx + 1]">
          {{ reportList[idx + 1].totalCredit | number: '1.2-2' }}
        </td>
        <td>
          {{ reportList[idx].documentTypeName }}
        </td>
        <td *ngIf="reportList[idx + 1]">
          {{ reportList[idx + 1].movementDate | date: "yyyy-MM-dd" }}
        </td>
      </tr>
      </tbody>
    </nz-table>
  </div>

  <!-- PDF Section -->
  <div #pdfTable id="pdfTable" class="pdf-container mt-4 text-center" dir="{{ lang === 'en' ? 'ltr' : 'rtl' }}">
    <!-- PDF Header -->
    <div class="text-center mb-4 border-bottom pb-3">
      <h2>{{ "REPORTS.ALLFINALREPORTS" | translate }}</h2>
      <div class="d-flex justify-content-between mt-2">
        <div>{{ 'PDF.DATE' | translate }}: {{ currentDate | date:'yyyy-MM-dd' }}</div>
        <div>{{ 'PDF.REF' | translate }}: {{ currentRef }}</div>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="filters-section mb-3 p-3 rounded text-center">
      <div class="grid-container">
        <!-- Section 1 -->
        <div class="section">
          <div>
            <p>{{ 'SYSTEMSETTINGS.DATERANGE' | translate }}:</p>
            <span>
          {{ form.value.startDate ? (form.value.startDate | date:'yyyy-MM-dd') : '-' }} -
              {{ form.value.endDate ? (form.value.endDate | date:'yyyy-MM-dd') : '-' }}
        </span>
          </div>
          <div>
            <p>{{ 'REPORTS.ACTIVITY' | translate }}:</p>
            <span>{{ getSelectedActivityNames() || 'All' }}</span>
          </div>
        </div>

        <!-- Section 2 -->
        <div class="section">
          <div>
            <p>{{ 'REPORTS.COSTCENTER' | translate }}:</p>
            <span>{{ getSelectedCostNames() || 'All' }}</span>
          </div>
          <div>
            <p>{{ 'REPORTS.PROJECT' | translate }}:</p>
            <span>{{ getSelectedProjectNames() || 'All' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Report Table -->
    <div *ngFor="let account of groupedReportList">
      <!-- Account Title -->
      <div class="filters-section">
        <div class=" grid-container font-bold p-2">
          <div class="section">
            <div>
              <p>{{ 'REPORTS.PARENTACCOUNT' | translate }}:</p>
              <span>{{ account.parentAccountNumber }}</span>
            </div>
            <div>
              <p>{{ 'REPORTS.ACCOUNT' | translate }}:</p>
              <span>{{ account.accountNumber }}</span>
            </div>
          </div>
          <div class="section">
            <div class="">

            </div>
            <div>
              <p>{{ 'ACCOUNT.ADDACCOUNTFORM.ACCOUNTNAME' | translate }}:</p>
              <span>{{ lang === 'en' ? account.accountNameEn : account.accountNameAr }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="table-responsive">
        <table class="table table-bordered table-striped">
          <thead class="table-light">
          <tr>

            <th class="text-center align-middle">{{ 'REPORTS.DOCUMENTNUMBER' | translate }}</th>
            <th class="text-center align-middle">{{ 'SALESACCOUNT.ACCOUNTNUMBER' | translate }}</th>
            <th class="text-center align-middle">{{ 'REPORTS.DESCRIPTION' | translate }}</th>
            <th class="text-center align-middle">{{ 'REPORTS.REFNUMBER' | translate }}</th>
            <th class="text-center align-middle">{{ 'REPORTS.TOTAL_DEBIT' | translate }}</th>
            <th class="text-center align-middle">{{ 'REPORTS.TOTAL_CREDIT' | translate }}</th>
            <th class="text-center align-middle">{{ 'REPORTS.MOVEMENTDATE' | translate }}</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let record of account.pairedRecords">
            <td class="text-center">
              {{ record.documentNumber }}
            </td>
            <td class="text-center">
              {{ account.accountNumber }} - {{ lang === 'en' ? account.accountNameEn : account.accountNameAr }}
            </td>
            <td class="text-center">
              {{ lang === 'en' ? record.descriptionEn : lang === 'ar' && record.descriptionAr }}
            </td>
            <td class="text-center">
              {{ record.refNumber }}
            </td>
            <td class="text-center">
              {{ record.totalDebit != null ? (record.totalDebit | number: '1.2-2') : '-' }}
            </td>
            <td class="text-center">
              {{ record.totalCredit != null ? (record.totalCredit | number: '1.2-2') : '-' }}
            </td>
            <td class="text-center">
              {{ record.movementDate ? (record.movementDate | date:'yyyy-MM-dd') : '-' }}
            </td>
          </tr>
          <!-- If no records available -->
          <tr *ngIf="account.pairedRecords.length === 0">
            <td colspan="3" class="text-center text-muted">
              {{ 'REPORTS.NO_DATA' | translate }}
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Export Button -->
  <div class="row mt-3 text-center">
    <app-export-pdf-button [tableElement]="pdfTable"></app-export-pdf-button>
  </div>
</div>
